import { FC, useCallback, useEffect, useState } from "react"
import { ViewStyle, Image, ImageStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { FedhaLoader, Screen } from "@/components"
import { useStores } from "@/store/rootStore"
import { colors } from "@/theme"

interface FedhaLoaderScreenProps extends AppStackScreenProps<"FedhaLoader"> {}

export const FedhaLoaderScreen: FC<FedhaLoaderScreenProps> = ({ navigation, route }) => {
  const { nextScreen } = route.params || {}
  const {
    auth: { isAuthenticated, user_type, isPhoneVerified, isInfoVerified, user },
  } = useStores()

  // State to track if the GIF is loading
  const [isImageLoading, setIsImageLoading] = useState(true)

  const handleNavigation = useCallback(() => {
    if (nextScreen) {
      navigation.reset({ index: 0, routes: [{ name: nextScreen as any }] })
    } else if (!isAuthenticated) {
      navigation.reset({ index: 0, routes: [{ name: "Welcome" }] })
    } else if (!isPhoneVerified) {
      navigation.reset({ index: 0, routes: [{ name: "OtpVerfication" }] })
    } else if (!isInfoVerified || !user?.is_documents_verified) {
      navigation.reset({ index: 0, routes: [{ name: "UserVerification" }] })
    } else {
      navigation.reset({
        index: 0,
        routes: [{ name: user_type === "standard" ? "TabNav" : "BusinessTabNav" }],
      })
    }
  }, [navigation, nextScreen, isAuthenticated, isPhoneVerified, isInfoVerified, user, user_type])

  useEffect(() => {
    // Simple timeout to show loading animation
    const timer = setTimeout(() => {
      handleNavigation()
    }, 1500)

    return () => clearTimeout(timer)
  }, [handleNavigation])

  return (
    <Screen
      style={$root}
      preset="fixed"
      StatusBarProps={{
        backgroundColor: colors.palette.neutral900,
      }}
    >
      {isImageLoading ? (
        <FedhaLoader size={280} />
      ) : (
        <Image
          source={require("../../../assets/annimations/fedhalogo.gif")}
          style={$animationStyle}
          onLoadStart={() => setIsImageLoading(true)}
          onLoadEnd={() => setIsImageLoading(false)}
        />
      )}
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $animationStyle: ImageStyle = {
  width: 400,
  height: 400,
  resizeMode: "contain",
}
