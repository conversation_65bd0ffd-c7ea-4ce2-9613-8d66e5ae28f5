/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { FC, useEffect, useState } from "react"
import { ViewStyle, View, FlatList, TextStyle, TouchableOpacity, Dimensions } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, WalletWidget, Text, Icon, ListScrol } from "@/components"
import { colors, spacing } from "@/theme"
import { formatDate } from "@/utils/dateUtils"
import { useStores } from "@/store/rootStore"
import React from "react"

interface PochiScreenProps extends AppStackScreenProps<"Pochi"> {}

const { height } = Dimensions.get("window")
const HEADER_HEIGHT = height * 0.1

const CurrencyItem = ({ amount, currency }: any) => (
  <View style={$currencyItem}>
    <View style={{ flexDirection: "row" }}>
      <Text style={$currencyAmount}>
        {amount.toLocaleString(undefined, {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}
      </Text>
      <Text style={$currencyCode}>{currency}</Text>
    </View>
  </View>
)

export const PochiScreen: FC<PochiScreenProps> = () => {
  const {
    fedhapochi: {
      wallet,
      fetchWallet,
      fetchPochiReport,
      pochiReport,
      reportOverview,
      fetchNextPage,
      hasMorePages,
    },
    appsettings: { currency },
  } = useStores()

  const [refreshing, setRefreshing] = useState(false)
  const [activeTab, setActiveTab] = useState<"wallet" | "report">("wallet")

  useEffect(() => {
    fetchWallet()
    fetchPochiReport()
  }, [fetchWallet, fetchPochiReport])

  const handleRefresh = async () => {
    setRefreshing(true)
    await Promise.all([fetchWallet(), fetchPochiReport()])
    setRefreshing(false)
  }

  const renderWalletTab = () => (
    <>
      <View style={$walletSection}>
        <WalletWidget
          walletname={wallet?.name ?? ""}
          walletstatus={wallet?.status ?? ""}
          walletnumber={wallet?.wallet ?? ""}
          walletbalance={wallet?.balance ?? 0}
        />
      </View>

      <View style={$currenciesContainer}>
        <Text style={$sectionTitle}>Available Currencies</Text>
        <View style={$sectionContent}>
          <FlatList
            data={currencies}
            keyExtractor={(item, index) => `${item.currency}-${index}`}
            renderItem={({ item }) => (
              <CurrencyItem amount={item.amount} currency={item.currency} />
            )}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            contentContainerStyle={$listContent}
          />
        </View>
      </View>
    </>
  )

  const renderReportTab = () => {
    // Transform your data into sections
    const groupTransactionsByDate = (transactions: any[]) => {
      // First, ensure each transaction has a unique identifier
      const transactionsWithUniqueIds = transactions.map((transaction, index) => {
        // Create a unique identifier if one doesn't exist
        if (!transaction._uniqueId) {
          return {
            ...transaction,
            _uniqueId: `${transaction.id || transaction.transaction || ""}_${index}`,
          }
        }
        return transaction
      })
      // console.log(transactions)

      const grouped = transactionsWithUniqueIds.reduce(
        (acc: { [key: string]: any[] }, transaction) => {
          // Use the utility function to format the date
          const date = formatDate(transaction.created_at)

          // Log for debugging
          // if (__DEV__ && transaction.created_at) {
          //   console.log(`Date string: ${transaction.created_at} -> Formatted: ${date}`)
          // }

          if (!acc[date]) {
            acc[date] = []
          }
          acc[date].push(transaction)
          return acc
        },
        {},
      )

      return Object.entries(grouped).map(([date, data]) => ({
        title: date,
        data,
      }))
    }

    const sections = groupTransactionsByDate(pochiReport?.transactions || [])

    // Handle loading more transactions when reaching the end of the list
    const handleLoadMore = () => {
      if (hasMorePages()) {
        fetchNextPage()
      }
    }

    return (
      <View style={$reportContainer}>
        <View style={$overviewCard}>
          <View style={$overviewItem}>
            <Text style={$overviewLabel}>Entrées</Text>
            <Text style={[$overviewAmount, { color: colors.palette.neutral800 }]}>
            {reportOverview.totalOut.toLocaleString()} {currency}
             
            </Text>
          </View>
          <View style={$separator} />
          <View style={$overviewItem}>
            <Text style={$overviewLabel}>Sorties</Text>
            <Text style={[$overviewAmount, { color: colors.palette.neutral800 }]}>
            {reportOverview.totalIn.toLocaleString()} {currency}
            </Text>
          </View>
        </View>

        <ListScrol
          sections={sections}
          onRefresh={handleRefresh}
          refreshing={refreshing}
          onEndReached={handleLoadMore}
          hasMoreData={hasMorePages()}
        />
      </View>
    )
  }

  // Create currencies array with default currency first
  const currencies = wallet
    ? [
        {
          amount: wallet.balance,
          currency: wallet.currency === "USD" ? "$" : "FC",
        },
        ...(wallet.other_currencies || []).map((curr) => ({
          amount: curr.amount,
          currency: curr.currency === "USD" ? "$" : "FC",
        })),
      ]
    : []

  return (
    <>
      <View style={$root}>
        <View style={$tabContainer}>
          <TouchableOpacity
            style={[$tabButton, activeTab === "wallet" && $activeTabButton]}
            onPress={() => setActiveTab("wallet")}
          >
            <View style={$tabContent}>
              <Icon
                icon="wallet"
                color={
                  activeTab === "wallet" ? colors.palette.neutral900 : colors.palette.accent300
                }
                size={20}
              />
              <Text style={[$tabText, activeTab === "wallet" && $activeTabText]}>Pochi</Text>
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            style={[$tabButton, activeTab === "report" && $activeTabButton]}
            onPress={() => setActiveTab("report")}
          >
            <View style={$tabContent}>
              <Icon
                icon="report"
                color={
                  activeTab === "report" ? colors.palette.neutral900 : colors.palette.accent300
                }
                size={20}
              />
              <Text style={[$tabText, activeTab === "report" && $activeTabText]}>Rapport</Text>
            </View>
          </TouchableOpacity>
        </View>
        {wallet && (activeTab === "report" ? renderReportTab() : renderWalletTab())}
        {/* <View style={$mainContainer}>
          <View style={$tabContainer}>
            <TouchableOpacity
              style={[$tabButton, activeTab === "wallet" && $activeTabButton]}
              onPress={() => setActiveTab("wallet")}
            >
              <View style={$tabContent}>
                <Icon
                  icon="wallet"
                  color={
                    activeTab === "wallet" ? colors.palette.neutral900 : colors.palette.accent300
                  }
                  size={20}
                />
                <Text style={[$tabText, activeTab === "wallet" && $activeTabText]}>Mon Pochi</Text>
              </View>
            </TouchableOpacity>
            <TouchableOpacity
              style={[$tabButton, activeTab === "report" && $activeTabButton]}
              onPress={() => setActiveTab("report")}
            >
              <View style={$tabContent}>
                <Icon
                  icon="report"
                  color={
                    activeTab === "report" ? colors.palette.neutral900 : colors.palette.accent300
                  }
                  size={20}
                />
                <Text style={[$tabText, activeTab === "report" && $activeTabText]}>Le Report</Text>
              </View>
            </TouchableOpacity>
          </View>
          {wallet && (activeTab === "report" ? renderReportTab() : renderWalletTab())}
        </View>
        */}
      </View>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  paddingTop: spacing.xl + spacing.md,
  backgroundColor: colors.palette.neutral100,
}

// Removed unused style

const $walletSection: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.md,
}

const $currenciesContainer: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.md,
  // flex: 1,  // Takes remaining space after wallet section
}

const $sectionTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  marginVertical: spacing.sm,
  color: colors.text,
}

const $listContent: ViewStyle = {
  paddingVertical: spacing.sm,
}

const $currencyItem: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingVertical: spacing.xs,
  paddingHorizontal: spacing.md,
  // backgroundColor: colors.palette.neutral100,
  // marginVertical: spacing.xs,
  // borderRadius: 8,
  // elevation: 1,
}

const $sectionContent: ViewStyle = {
  borderRadius: 12,
  backgroundColor: colors.palette.neutral100,
  marginTop: spacing.xs,
  shadowColor: colors.palette.accent500,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  // Shadow for Android
  elevation: 4,
  // Optional: subtle border
  borderWidth: 1,
  borderColor: colors.palette.neutral500,
  // flex: 1, // Takes all available space in currencies container
}

const $currencyAmount: TextStyle = {
  fontSize: 17,
  fontWeight: "500",
  color: colors.text,
}

const $currencyCode: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.textDim,
  marginHorizontal: 5,
}

const $reportContainer: ViewStyle = {
  paddingHorizontal: spacing.lg,
}

const $overviewCard: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.md,
  marginBottom: spacing.lg,
  // Shadow for iOS
  shadowColor: colors.palette.accent500,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  // Shadow for Android
  elevation: 4,
  // Optional: subtle border
  borderWidth: 1,
  borderColor: colors.palette.neutral500,
  // // Optional: gradient-like effect with border
  // borderLeftWidth: 4,
  // borderLeftColor: colors.palette.accent500,
}

const $overviewItem: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  marginBottom: spacing.xs,
}

const $overviewLabel: TextStyle = {
  fontSize: 14,
  fontWeight: "bold",
  color: colors.text,
}

const $overviewAmount: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.text,
}

const $separator: ViewStyle = {
  height: 1,
  backgroundColor: colors.palette.accent200,
  marginVertical: spacing.sm,
}

const $tabContainer: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: spacing.md,
  paddingVertical: 20,
  height: HEADER_HEIGHT,
  // backgroundColor: colors.palette.neutral100,
}

const $tabButton: ViewStyle = {
  flex: 1,
  // paddingVertical: spacing.md,
  borderBottomWidth: 2,
  borderBottomColor: colors.palette.neutral300,
}

const $activeTabButton: ViewStyle = {
  borderBottomColor: colors.palette.neutral900,
}

const $tabContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.xs,
}

const $tabText: TextStyle = {
  fontSize: 16,
  color: colors.palette.accent400,
  marginLeft: spacing.xs,
}

const $activeTabText: TextStyle = {
  color: colors.palette.neutral900,
  fontWeight: "bold",
}
