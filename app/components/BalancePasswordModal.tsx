import React, { useEffect, useState } from "react"
import {
  Modal,
  StyleSheet,
  View,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Dimensions,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
} from "react-native"
import { colors, spacing } from "@/theme"
import { Text, TextField, Button, Icon } from "."
import { useStores } from "@/store/rootStore"

export interface BalancePasswordModalProps {
  isVisible: boolean
  onClose: () => void
  onSuccess: () => void
}

export const BalancePasswordModal = (props: BalancePasswordModalProps) => {
  const { isVisible, onClose, onSuccess } = props
  const [password, setPassword] = useState("")
  const [isPasswordHidden, setIsPasswordHidden] = useState(true)
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  const {
    auth: { user },
  } = useStores()

  // Reset state when modal opens
  useEffect(() => {
    if (isVisible) {
      setPassword("")
      setError("")
      setIsLoading(false)
    }
  }, [isVisible])

  const handleVerifyPassword = async () => {
    if (!password.trim()) {
      setError("Veuillez entrer votre mot de passe")
      return
    }

    setIsLoading(true)

    try {
      // For demo purposes, we'll use a simple check
      // In a real app, you would verify against the user's actual password
      // via an API call or secure storage

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 800))

      // For demo, we'll accept any password with length >= 4
      // Replace this with actual password verification
      if (password.length >= 4) {
        onSuccess()
        onClose()
      } else {
        setError("Mot de passe incorrect")
      }
    } catch (err) {
      setError("Une erreur s'est produite. Veuillez réessayer.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackdropPress = () => {
    // Close modal when clicking outside
    Keyboard.dismiss()
    onClose()
  }

  const handleModalContentPress = (e: any) => {
    // Prevent closing when clicking on modal content
    e.stopPropagation()
  }

  return (
    <Modal
      visible={isVisible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <TouchableWithoutFeedback onPress={handleBackdropPress}>
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : undefined}
          style={$modalOverlay}
        >
          <TouchableWithoutFeedback onPress={handleModalContentPress}>
            <View style={$modalContent}>
              <View style={$modalHeader}>
                <Icon
                  icon="lock"
                  size={40}
                  color={colors.palette.primary500}
                  containerStyle={$iconContainer}
                />
                <TouchableOpacity style={$closeButton} onPress={onClose}>
                  <Icon icon="x" size={24} color={colors.palette.neutral600} />
                </TouchableOpacity>
              </View>

              <Text style={$title}>Vérification de sécurité</Text>
              <Text style={$subtitle}>
                Veuillez entrer votre mot de passe pour afficher votre solde
              </Text>

              <View style={$inputContainer}>
                <TextField
                  value={password}
                  onChangeText={setPassword}
                  placeholder="Entrez votre mot de passe"
                  secureTextEntry={isPasswordHidden}
                  autoCapitalize="none"
                  autoCorrect={false}
                  containerStyle={$textFieldContainer}
                  status={error ? "error" : undefined}
                  helper={error}
                  RightAccessory={() => (
                    <TouchableOpacity
                      style={$eyeIconContainer}
                      onPress={() => setIsPasswordHidden(!isPasswordHidden)}
                    >
                      <Icon
                        icon={isPasswordHidden ? "view" : "hidden"}
                        size={20}
                        color={colors.palette.neutral600}
                      />
                    </TouchableOpacity>
                  )}
                  onSubmitEditing={handleVerifyPassword}
                />
              </View>

              <View style={$buttonContainer}>
                <Button
                  preset="default"
                  text="Annuler"
                  style={$cancelButton}
                  onPress={onClose}
                />
                <Button
                  preset="reversed"
                  text={isLoading ? "Vérification..." : "Confirmer"}
                  style={$confirmButton}
                  disabled={isLoading || !password.trim()}
                  onPress={handleVerifyPassword}
                />
              </View>
            </View>
          </TouchableWithoutFeedback>
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </Modal>
  )
}

const { width } = Dimensions.get("window")

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.lg,
  width: width * 0.85,
  maxWidth: 400,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  elevation: 5,
}

const $modalHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.md,
  position: "relative",
}

const $iconContainer: ViewStyle = {
  backgroundColor: colors.palette.primary100,
  width: 80,
  height: 80,
  borderRadius: 40,
  justifyContent: "center",
  alignItems: "center",
}

const $closeButton: ViewStyle = {
  position: "absolute",
  right: 0,
  top: 0,
  padding: spacing.xs,
}

const $title: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  textAlign: "center",
  marginTop: spacing.sm,
}

const $subtitle: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginTop: spacing.xs,
  marginBottom: spacing.lg,
}

const $inputContainer: ViewStyle = {
  marginBottom: spacing.lg,
}

const $textFieldContainer: ViewStyle = {
  marginBottom: 0,
}

const $eyeIconContainer: ViewStyle = {
  padding: spacing.xs,
}

const $buttonContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
}

const $cancelButton: ViewStyle = {
  flex: 1,
  marginRight: spacing.xs,
  backgroundColor: colors.palette.neutral200,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $confirmButton: ViewStyle = {
  flex: 1,
  marginLeft: spacing.xs,
  backgroundColor: colors.palette.primary600,
}
