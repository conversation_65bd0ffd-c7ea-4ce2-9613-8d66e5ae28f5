/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import { useState, useEffect } from "react"
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
} from "react-native"
import { colors, spacing } from "@/theme"
import { Text, Icon, FencyTextInput, Button, FedhaPochiInput, ChangeCurency } from "."
import { getUserByPochiID, TransferCash } from "@/services/api/api"
import { useStores } from "@/store/rootStore"
import { Avatar } from "react-native-paper"
import { getNameInitials } from "@/utils/actions"
import { Controller, useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import React from "react"

interface Beneficiary {
  id: string
  beneficiary_name: string
  wallet: string
  lastAmount: number
  profile_image?: any
  banner_image?: any
}

interface AddBeneficiaryModalProps {
  visible: boolean
  onClose: () => void
  onAdd: (beneficiary: Omit<Beneficiary, "id" | "lastAmount">) => void
}

const AddBeneficiaryModal = ({ visible, onClose, onAdd }: AddBeneficiaryModalProps) => {
  const [walletId, setWalletId] = useState("")
  const [isVerifying, setIsVerifying] = useState(false)
  const [isVerified, setIsVerified] = useState(false)
  const [userDetails, setUserDetails] = useState<{
    firstName: string
    lastName: string
    walletId: string
  } | null>(null)

  const handleVerifyWallet = async () => {
    if (!walletId.trim()) {
      Alert.alert("Erreur", "Veuillez saisir un ID de portefeuille")
      return
    }

    setIsVerifying(true)
    try {
      const response = await getUserByPochiID(walletId)
      if (response.success && response.data?.details) {
        const details = response.data.details
        const userInfo = {
          firstName: details.first_name,
          lastName: details.last_name,
          walletId: details.wallet_id,
        }
        setUserDetails(userInfo)

        Alert.alert(
          "Vérification réussie",
          `Voulez-vous ajouter ${details.first_name} ${details.last_name} comme bénéficiaire?`,
          [
            {
              text: "Non",
              onPress: () => {
                setWalletId("")
                setUserDetails(null)
                setIsVerified(false)
              },
              style: "cancel",
            },
            {
              text: "Oui",
              onPress: () => setIsVerified(true),
            },
          ],
        )
      } else {
        Alert.alert("Erreur", "Impossible de trouver cet utilisateur")
        setIsVerified(false)
      }
    } catch (error: any) {
      console.log(error)
      Alert.alert("Erreur", "Erreur lors de la vérification du portefeuille")
      setIsVerified(false)
    } finally {
      setIsVerifying(false)
    }
  }

  const handleAdd = () => {
    if (!isVerified || !userDetails) {
      Alert.alert("Erreur", "Veuillez d'abord vérifier le portefeuille")
      return
    }

    onAdd({
      beneficiary_name: `${userDetails.firstName} ${userDetails.lastName}`,
      wallet: userDetails.walletId,
      profile_image: null,
    })

    // Reset the form
    setWalletId("")
    setUserDetails(null)
    setIsVerified(false)
    onClose()
  }

  return (
    <Modal visible={visible} transparent animationType="slide">
      <View style={$modalOverlay}>
        <View style={$modalContent}>
          <Text preset="heading" style={$modalTitle}>
            Ajouter un bénéficiaire
          </Text>

          {!isVerified && <FedhaPochiInput onChange={setWalletId} value={walletId} />}

          {!isVerified && (
            <Button
              text={isVerifying ? "Vérification..." : "Vérifier"}
              preset="reversed"
              onPress={handleVerifyWallet}
              style={$verifyButton}
              disabled={isVerifying || !walletId.trim()}
            />
          )}

          {isVerified && userDetails && (
            <View style={$verifiedInfo}>
              <Text preset="subheading">Détails du bénéficiaire</Text>
              <Text style={$verifiedName}>
                {userDetails.firstName} {userDetails.lastName}
              </Text>
              <Text style={$walletIdText}>Wallet ID: {userDetails.walletId}</Text>
            </View>
          )}

          <View style={$modalButtons}>
            <Button text="Annuler" preset="default" onPress={onClose} style={$modalButton} />
            <Button
              text="Ajouter"
              preset="reversed"
              onPress={handleAdd}
              style={[$modalButton, !isVerified && { opacity: 0.5 }]}
              disabled={!isVerified}
            />
          </View>
        </View>
      </View>
    </Modal>
  )
}

interface SendMoneyModalProps {
  visible: boolean
  beneficiary: Beneficiary | null
  onClose: () => void
  onSend: (amount: string, note: string) => void
}

const SendMoneyModal = ({ visible, beneficiary, onClose, onSend }: SendMoneyModalProps) => {
  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  const {
    appsettings: { currency, getExchangeRate },
    fedhapochi: { currentBalance },
  } = useStores()

  // Create the schema with the current currency
  const paymentSchema = createPaymentSchema(currency)

  // Setup form with validation
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<PaymentFormData>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      amount: "",
      note: "",
      currency: currency,
      receiver: beneficiary?.wallet || "",
    },
  })

  // Reset form when modal is closed
  useEffect(() => {
    if (!visible) {
      reset()
    }
  }, [visible, reset])

  // Handle form submission
  const onSubmit = (data: PaymentFormData) => {
    // Validate that the amount is not greater than the current balance
    const amountValue = Number(data.amount)

    // Check if amount exceeds current balance
    if (amountValue > currentBalance) {
      Alert.alert(
        "Erreur",
        `Le montant ne peut pas dépasser votre solde actuel de ${currentBalance} FC`,
      )
      return
    }

    // Check minimum amount for FC (this is also validated by the schema, but double-checking here)
    if (currency === "FC" && amountValue < 2000) {
      Alert.alert("Erreur", "Le montant minimum pour FC est de 2000")
      return
    }

    onSend(data.amount, data.note || "")
  }

  return (
    <>
      <Modal visible={visible} transparent animationType="slide">
        <View style={$modalOverlay}>
          <View style={$modalContent}>
            <Text preset="heading" style={$modalTitle}>
              Envoyer de l&lsquo;argent
            </Text>
            <View style={$beneficiaryInfo}>
              <Text preset="subheading">{beneficiary?.beneficiary_name}</Text>
              <Text style={$walletId}>{beneficiary?.wallet}</Text>
            </View>
            <Controller
              control={control}
              name="amount"
              rules={{
                required: "Le montant est obligatoire",
                validate: (value) => {
                  const amount = parseFloat(value)
                  if (isNaN(amount)) return "Le montant doit être un nombre valide"

                  // Get exchange rate and convert amounts using API rates
                  if (currency === "USD") {
                    if (amount < 1) return "Le montant minimum est de 2 USD"
                    // Convert balance to USD using API exchange rate
                    const balanceInUSD = getExchangeRate("FC", "USD") * currentBalance
                    if (amount > balanceInUSD)
                      return `Solde insuffisant. Votre solde est de ${balanceInUSD.toFixed(2)} USD`
                  } else if (currency === "FC") {
                    if (amount < 2000) return "Le montant minimum est de 2000 FC"
                    // Convert balance to FC using API exchange rate
                    const balanceInFC = getExchangeRate("USD", "FC") * currentBalance
                    if (amount > balanceInFC)
                      return `Solde insuffisant. Votre solde est de ${balanceInFC.toFixed(2)} FC`
                  }
                  return true
                },
              }}
              render={({ field: { onChange, value } }) => (
                <View>
                  <FencyTextInput
                    value={value}
                    onChange={onChange}
                    inputname="Montant a Payer"
                    keyboardType="numeric"
                    placeholder="Saisir le montant"
                    LeftIcon={currency === "FC" ? "FC" : currency === "USD" ? "dollar" : ""}
                    leftonPress={() => setShowCurrencyModal(!showCurrencyModal)}
                    status={errors.amount ? "error" : undefined}
                  />
                  {errors.amount && <Text style={$formErrorText}>{errors.amount.message}</Text>}
                </View>
              )}
            />

            <Controller
              control={control}
              name="note"
              render={({ field: { onChange, value } }) => (
                <FencyTextInput
                  value={value}
                  onChange={onChange}
                  inputname="Note (optionnel)"
                  placeholder="Ajouter une note pour ce paiement"
                  style={{ marginTop: spacing.sm }}
                />
              )}
            />
            <View style={$modalButtons}>
              <Button text="Annuler" preset="default" onPress={onClose} style={$modalButton} />
              <Button
                text="Envoyer"
                preset="reversed"
                onPress={handleSubmit(onSubmit)}
                style={$modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
      <ChangeCurency isVisible={showCurrencyModal} onClose={() => setShowCurrencyModal(false)} />
    </>
  )
}


export interface BeneficiairesProps {
  style?: StyleProp<ViewStyle>
}

// Create a schema factory function that takes the currency as a parameter
const createPaymentSchema = (currency: string) =>
  z.object({
    amount: z
      .string()
      .min(1, "Le montant est obligatoire")
      .refine((val) => !isNaN(Number(val)), {
        message: "Le montant doit être un nombre valide",
      })
      .refine((val) => Number(val) > 0, {
        message: "Le montant doit être supérieur à 0",
      })
      .refine(
        (val) => {
          // If currency is FC, amount must be at least 2000
          if (currency === "FC") {
            return Number(val) >= 2000
          }
          // For other currencies, no minimum amount
          return true
        },
        {
          message: currency === "FC" ? "Le montant minimum pour FC est de 2000" : "",
        },
      ),
    note: z.string().optional(),
    receiver: z.string().optional(),
    currency: z.string().optional(),
  })

// Define the PaymentFormData type based on the schema
type PaymentFormData = z.infer<ReturnType<typeof createPaymentSchema>>

export const Beneficiaires = (props: BeneficiairesProps) => {
  const { style } = props
  const $styles = [$container, style]
  const [selectedBeneficiary, setSelectedBeneficiary] = useState<Beneficiary | null>(null)
  const [modalVisible, setModalVisible] = useState(false)
  const [addModalVisible, setAddModalVisible] = useState(false)

  const {
    beneficiary: { beneficiaries, fetchBeneficiaries, addBeneficiary, loading, error },
    appsettings: { currency, getServerCurrencyCode },
  } = useStores()

  useEffect(() => {
    fetchBeneficiaries()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const handleAddBeneficiary = async (newBeneficiary: Omit<Beneficiary, "id" | "lastAmount">) => {
    const result = await addBeneficiary({
      wallet: newBeneficiary.wallet,
      beneficiary_name: newBeneficiary.beneficiary_name,
    })
    // console.log('result', result)
    if (result) {
      await fetchBeneficiaries()
      setAddModalVisible(false)
    }
  }

  // Beneficiaries data is now properly handled

  const handleSendMoney = async (amount: string, note: string) => {
    try {
      if (!selectedBeneficiary) {
        Alert.alert("Erreur", "Aucun bénéficiaire sélectionné")
        return
      }

      // Form validation is now handled by react-hook-form and the onSubmit function

      const requestData = {
        receiver: selectedBeneficiary.wallet,
        amount: Number(amount),
        currency: getServerCurrencyCode(currency),
        note: note || "Paiement via Lipa na Fedha",
      }

      const response = await TransferCash(requestData)

      if (response.success) {
        Alert.alert("Succès", "Paiement effectué avec succès", [
          {
            text: "OK",
            onPress: () => {
              setModalVisible(false)
              setSelectedBeneficiary(null)
            },
          },
        ])
      } else {
        throw new Error(response.message || "Le paiement a échoué")
      }
    } catch (error: any) {
      console.error("Payment Error:", error)
      Alert.alert(
        "Erreur",
        error.message || "Une erreur s'est produite lors du paiement. Veuillez réessayer.",
      )
    } finally {
      setModalVisible(false)
      setSelectedBeneficiary(null)
    }
  }

  const renderBeneficiaryItem = ({ item }: { item: any }) => (
  
    <TouchableOpacity
      style={$recipientItem}
      onPress={() => {
        setSelectedBeneficiary(item)
        setModalVisible(true)
      }}
      activeOpacity={0.7}
    >
      <View style={$recipientInfo}>
        {item.profile_image ? (
          <Avatar.Image size={56} source={{ uri: item.profile_image }} style={$avatar} />
        ) : (
          <Avatar.Text
            size={56}
            label={getNameInitials(item.beneficiary_name || item.name || "")}
            style={[$avatar, { backgroundColor: colors.palette.primary500 }]}
            labelStyle={[$avatarLabel, { color: colors.palette.neutral100 }]}
          />
        )}
        <View style={$textContainer}>
          <Text style={$nameText} numberOfLines={1}>
            {item.beneficiary_name}
          </Text>
          <View style={$walletContainer}>
            <Icon icon="wallet" size={14} color={colors.palette.primary500} style={$walletIcon} />
            <Text style={$walletText} numberOfLines={1}>
              {item.wallet}
            </Text>
          </View>
        </View>
      </View>
      <View style={$actionContainer}>
        <Text style={$sendText}>Envoyer</Text>
        <Icon icon="caretRight" color={colors.palette.primary500} size={16} />
      </View>
    </TouchableOpacity>
  )

 

  return (
    <View style={$styles}>
      <View style={$header}>
        <TouchableOpacity style={$addButton} onPress={() => setAddModalVisible(true)}>
          <Icon icon="plus" size={24} color={colors.palette.primary500} />
        </TouchableOpacity>
        <Text preset="subheading" style={$headerText}>Bénéficiaires</Text>
      </View>
      <ScrollView
            contentContainerStyle={$listContent}
            showsVerticalScrollIndicator={false}
          >
      {beneficiaries.map((item) => (
              <React.Fragment key={item?.wallet || item?.id}>
                {renderBeneficiaryItem({ item })}
              </React.Fragment>
            ))}
</ScrollView>
      {/* {loading ? (
        <View style={$loadingContainer}>
          <Icon icon="loading" size={40} color={colors.palette.primary500} />
          <Text style={$loadingText}>Chargement des bénéficiaires...</Text>
        </View>
      ) : error ? (
        <View style={$errorContainer}>
          <Icon icon="caretRight" size={40} color={colors.palette.angry500} />
          <Text style={$errorText}>{error}</Text>
          <TouchableOpacity style={$retryButton} onPress={fetchBeneficiaries}>
            <Text style={$retryText}>Réessayer</Text>
          </TouchableOpacity>
        </View>
      ) : beneficiaries?.length > 0 ? (
        <View style={$listContainer}>
          <ScrollView
            contentContainerStyle={$listContent}
            showsVerticalScrollIndicator={false}
          >
            {beneficiaries.map((item) => (
              <React.Fragment key={item?.wallet || item?.id}>
                {renderBeneficiaryItem({ item })}
              </React.Fragment>
            ))}
          </ScrollView>
        </View>
      ) : (
        <View style={$emptyContainer}>
          <View style={$emptyIconContainer}>
            <Icon icon="group" size={40} color={colors.palette.primary500} />
          </View>
          <Text style={$emptyTitle}>Aucun bénéficiaire</Text>
          <Text style={$emptyText}>
            Ajoutez des bénéficiaires pour effectuer des transferts plus rapidement
          </Text>
          <TouchableOpacity style={$addEmptyButton} onPress={() => setAddModalVisible(true)}>
            <Icon icon="plus" size={20} color={colors.palette.neutral100} style={$addEmptyIcon} />
            <Text style={$addEmptyText}>Ajouter un bénéficiaire</Text>
          </TouchableOpacity>
        </View>
      )} */}

      <SendMoneyModal
        visible={modalVisible}
        beneficiary={selectedBeneficiary}
        onClose={() => {
          setModalVisible(false)
          setSelectedBeneficiary(null)
        }}
        onSend={handleSendMoney}
      />

      <AddBeneficiaryModal
        visible={addModalVisible}
        onClose={() => setAddModalVisible(false)}
        onAdd={handleAddBeneficiary}
      />
    </View>
  )
}

const $container: ViewStyle = {
  flex: 1,
  // backgroundColor: colors.background,
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.xl,
  // borderBottomWidth: 1,
  // borderBottomColor: colors.palette.neutral300,
  // backgroundColor: colors.palette.neutral100,
}

const $addButton: ViewStyle = {
  width: 35,
  height: 35,
  borderRadius: 22,
  backgroundColor: colors.palette.primary100,
  alignItems: "center",
  justifyContent: "center",
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 2,
}
const $headerText: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.palette.neutral800,
}

const $listContainer: ViewStyle = {
  // flex: 1,
  paddingHorizontal: spacing.md,
}

const $listContent: ViewStyle = {
  padding: spacing.sm,
  flex: 1,
  // backgroundColor: colors.palette.neutral200,
}

const $recipientItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  padding: spacing.md,
  backgroundColor: colors.palette.neutral200,
  borderRadius: 12,
  marginBottom: spacing.sm,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 2,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
}

const $recipientInfo: ViewStyle = {
  flex: 1,
  flexDirection: "row",
  alignItems: "center",
}

const $avatar: ViewStyle = {
  backgroundColor: colors.palette.primary200,
  marginRight: spacing.sm,
}

const $avatarLabel: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.primary100,
}

const $textContainer: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $nameText: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.text,
  marginBottom: 4,
}

const $walletText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  fontWeight: "400",
}

const $walletContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginTop: 4,
}

const $walletIcon = {
  marginRight: 4,
} as any

const $actionContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.neutral100,
  paddingHorizontal: 12,
  paddingVertical: 8,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.palette.primary100,
}

const $sendText: TextStyle = {
  fontSize: 14,
  color: colors.palette.primary500,
  fontWeight: "600",
  marginRight: 4,
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  padding: spacing.xl,
}

const $loadingText: TextStyle = {
  marginTop: spacing.md,
  fontSize: 16,
  color: colors.palette.neutral600,
  textAlign: "center",
}

const $errorContainer: ViewStyle = {
  // flex: 1,
  alignItems: "center",
  justifyContent: "center",
  padding: spacing.xl,
}

const $errorText: TextStyle = {
  marginTop: spacing.md,
  marginBottom: spacing.lg,
  fontSize: 16,
  color: colors.palette.angry500,
  textAlign: "center",
}

const $retryButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  borderRadius: 8,
}

const $retryText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: "500",
}

const $emptyContainer: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  padding: spacing.xl,
}

const $emptyIconContainer: ViewStyle = {
  width: 80,
  height: 80,
  borderRadius: 40,
  backgroundColor: colors.palette.primary100,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.md,
}

const $emptyTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.palette.neutral800,
  marginBottom: spacing.xs,
}

const $emptyText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginBottom: spacing.lg,
}

const $addEmptyButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: colors.palette.primary500,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  borderRadius: 8,
}

const $addEmptyIcon = {
  marginRight: spacing.xs,
} as any

const $addEmptyText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: "500",
}

const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
}

const $modalContent: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: spacing.lg,
  borderRadius: 12,
  width: "90%",
}

const $modalTitle: TextStyle = {
  textAlign: "center",
  marginBottom: spacing.lg,
}

const $beneficiaryInfo: ViewStyle = {
  alignItems: "center",
  marginBottom: spacing.lg,
}

const $walletId: TextStyle = {
  color: colors.palette.neutral600,
  marginTop: spacing.xs,
}

const $modalButtons: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  marginTop: spacing.lg,
}

const $modalButton: ViewStyle = {
  flex: 1,
  marginHorizontal: spacing.xs,
}

const $verifyButton: ViewStyle = {
  marginTop: spacing.sm,
  marginBottom: spacing.md,
}

const $verifiedInfo: ViewStyle = {
  alignItems: "center",
  marginVertical: spacing.md,
  padding: spacing.sm,
  backgroundColor: colors.palette.neutral200,
  borderRadius: 8,
}

const $verifiedName: TextStyle = {
  color: colors.palette.primary500,
  fontSize: 16,
  marginTop: spacing.xs,
  fontWeight: "bold",
}

const $walletIdText: TextStyle = {
  color: colors.palette.neutral600,
  fontSize: 14,
  marginTop: spacing.xs,
}

const $formErrorText: TextStyle = {
  color: colors.error,
  fontSize: 12,
  marginTop: spacing.xs,
}
