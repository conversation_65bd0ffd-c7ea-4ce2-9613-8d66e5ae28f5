/* eslint-disable import/no-unresolved */
import { StyleProp, View, ViewStyle } from "react-native"
import { $Gstyles, colors } from "@/theme"
import { Icon, Text } from "."
import { useStores } from "@/store/rootStore"
import { useMemo } from "react"

export interface FedhaPochiBarProps {
  style?: StyleProp<ViewStyle>
  amount?: number
}

export const FedhaPochiBar = (props: FedhaPochiBarProps) => {
  const { style, amount } = props
  const $styles = [$container, style]
  const {
    fedhapochi: { wallet, currentBalance, otherCurrencies },
    appsettings: { currency, getStandardizedCurrency },
  } = useStores()

  const displayCurrency = useMemo(() => {
    return getStandardizedCurrency(currency)
  }, [currency, getStandardizedCurrency])

  // Format balance with proper currency
  const formattedBalance = useMemo(() => {
    // If amount prop is provided, use it directly
    // Otherwise, use the currentBalance from the store
    let value = amount !== undefined ? amount : currentBalance || 0

    // If currency is USD and amount prop is not provided, we need to convert
    if (displayCurrency === "USD" && amount === undefined) {
      const usdBalance = otherCurrencies?.find(
        (curr) => getStandardizedCurrency(curr.currency) === "USD",
      )
      value = usdBalance ? usdBalance.amount : value / 2900 // Fallback to conversion
    }

    return value.toLocaleString(displayCurrency === "FC" ? "fr-CD" : "en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      useGrouping: true,
    })
  }, [amount, currentBalance, displayCurrency, getStandardizedCurrency, otherCurrencies])

  return (
    <View style={[$Gstyles.boxcomp, $styles]}>
      <View>
        <Icon
          icon="fedhapochi"
          color={colors.palette.neutral900}
          size={30}
          containerStyle={$ActionIcon}
        />
      </View>

      <View>
        <Text preset="subheading" style={$Gstyles.serviceName}>
          {wallet?.name}
        </Text>
        <View>
          <Text preset="formHelper">
            {displayCurrency} {formattedBalance}
          </Text>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  // justifyContent: "center",
}

const $ActionIcon: ViewStyle = {
  padding: 10,
  marginRight: 20,
  alignItems: "center",
  justifyContent: "center",
  alignContent: "center",
  borderRadius: 50,
}
