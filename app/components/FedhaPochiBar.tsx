/* eslint-disable import/no-unresolved */
import { StyleProp, View, ViewStyle } from "react-native"
import { $Gstyles, colors } from "@/theme"
import { Icon, Text } from "."
import { useStores } from "@/store/rootStore"
import { useMemo } from "react"

export interface FedhaPochiBarProps {
  style?: StyleProp<ViewStyle>
  amount?: number
}

export const FedhaPochiBar = (props: FedhaPochiBarProps) => {
  const { style, amount } = props
  const $styles = [$container, style]
  const {
    fedhapochi: { wallet, otherCurrencies },
    appsettings: { currency, getStandardizedCurrency },
  } = useStores()

  // console.log('cr', currency)

  const displayCurrency = useMemo(() => {
    return getStandardizedCurrency(currency)
  }, [currency, getStandardizedCurrency])

  // Get the raw balance directly from API data without any formatting or conversion
  const displayBalance = useMemo(() => {
    // If amount prop is provided, use it directly
    if (amount !== undefined) {
      return amount
    }

    // If currency is FC, use the main balance from wallet
    if (displayCurrency === "FC") {
      return wallet?.balance || 0
    }

    // If currency is USD, get the amount from other_currencies array
    if (displayCurrency === "USD") {
      const usdBalance = otherCurrencies?.find(
        (curr) => getStandardizedCurrency(curr.currency) === "USD",
      )
      return usdBalance ? usdBalance.amount : 0
    }

    // Fallback to main balance for any other currency
    return wallet?.balance || 0
  }, [amount, wallet?.balance, displayCurrency, getStandardizedCurrency, otherCurrencies])

  return (
    <View style={[$Gstyles.boxcomp, $styles]}>
      <View>
        <Icon
          icon="fedhapochi"
          color={colors.palette.neutral900}
          size={30}
          containerStyle={$ActionIcon}
        />
      </View>

      <View>
        <Text preset="subheading" style={$Gstyles.serviceName}>
          {wallet?.name}
        </Text>
        <View>
          <Text preset="formHelper">
            {displayCurrency} {displayBalance}
          </Text>
        </View>
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  // justifyContent: "center",
}

const $ActionIcon: ViewStyle = {
  padding: 10,
  marginRight: 20,
  alignItems: "center",
  justifyContent: "center",
  alignContent: "center",
  borderRadius: 50,
}
