/* eslint-disable import/no-unresolved */
import { StyleProp, TextStyle, TouchableOpacity, ViewStyle } from "react-native"
import { colors, spacing } from "@/theme"
import { ChangeCurency, Icon, Text, BalancePasswordModal } from "."
import { useMemo, useState, useEffect } from "react"
import { useStores } from "@/store/rootStore"
import React from "react"

export interface WalletBalanceWidgetProps {
  amount: any
  style?: StyleProp<ViewStyle>
}

export const WalletBalanceWidget = (props: WalletBalanceWidgetProps) => {
  const {
    appsettings: { currency, setCurrency, getStandardizedCurrency },
    fedhapochi: { updateCurrentBalance, wallet, fetchWallet },
  } = useStores()

  const { style, amount } = props
  const $styles = [$container, style]

  const [isHidden, setIsHidden] = useState(true)
  const [showCurrencyModal, setShowCurrencyModal] = useState(false)
  const [showPasswordModal, setShowPasswordModal] = useState(false)
  // fedhapochi: { otherCurrencies, updateCurrentBalance },
  // console.log("otherCurrencies", otherCurrencies)
  // Set default currency to FC if not set
  useEffect(() => {
    if (!currency) {
      setCurrency("FC")
      updateCurrentBalance("FC")
    }
  }, [])

  useEffect(() => {
    fetchWallet()
  }, [fetchWallet])

  const displayCurrency = useMemo(() => {
    return getStandardizedCurrency(currency)
  }, [currency, getStandardizedCurrency])

  // Format balance with proper currency
  const formattedBalance = useMemo(() => {
    if (isHidden) return "******"

    let value = amount // Default FC balance

    // If wallet exists and currency is not FC, find the corresponding balance
    if (wallet && displayCurrency !== "FC") {
      const selectedCurrencyBalance = wallet.other_currencies?.find(
        (curr) => getStandardizedCurrency(curr.currency) === displayCurrency,
      )
      value = selectedCurrencyBalance ? selectedCurrencyBalance.amount : 0
    }

    return value.toLocaleString(displayCurrency === "FC" ? "fr-CD" : "en-US", {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      useGrouping: true,
    })
  }, [amount, displayCurrency, getStandardizedCurrency, isHidden, wallet])

  const balanceStyle = useMemo(() => {
    const length = formattedBalance.length
    let fontSize = 33

    if (length > 12) {
      fontSize = 26
    } else if (length > 9) {
      fontSize = 29
    }

    return {
      fontSize,
      fontWeight: 600,
      color: colors.palette.neutral800,
      textAlign: "center",
      paddingRight: spacing.md,
      paddingTop: spacing.md,
    } as TextStyle
  }, [formattedBalance])

  return (
    <>
      <TouchableOpacity style={$styles} onPress={() => setShowCurrencyModal(true)}>
        <Text style={balanceStyle} numberOfLines={1} ellipsizeMode="tail">
          {`${displayCurrency} ${formattedBalance}`}
        </Text>
        <Icon
          icon={isHidden ? "hidden" : "view"}
          color={colors.palette.neutral900}
          size={20}
          onPress={(e) => {
            e.stopPropagation()
            // If balance is already visible, hide it directly
            if (!isHidden) {
              setIsHidden(true)
            } else {
              // If balance is hidden, show password modal
              setShowPasswordModal(true)
            }
          }}
        />
      </TouchableOpacity>
      <ChangeCurency isVisible={showCurrencyModal} onClose={() => setShowCurrencyModal(false)} />
      <BalancePasswordModal
        isVisible={showPasswordModal}
        onClose={() => setShowPasswordModal(false)}
        onSuccess={() => setIsHidden(false)}
      />
    </>
  )
}

const $container: ViewStyle = {
  alignItems: "center",
  flexDirection: "row",
}
