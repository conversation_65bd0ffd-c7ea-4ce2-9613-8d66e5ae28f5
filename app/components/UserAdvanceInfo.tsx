/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-color-literals */
/* eslint-disable react-native/no-inline-styles */
import { StyleProp, View, ViewStyle, Alert, StyleSheet } from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, UserVerificationStyles } from "@/theme"
import DatePicker from "react-native-date-picker"
import { Button, CustomSelector, FencyTextInput, Text } from "."
import { zodResolver } from "@hookform/resolvers/zod"
import { Controller, useForm } from "react-hook-form"
import { userAdvancedInfoUpdate, UserAdvancedSchema } from "@/services/api"
import { useStores } from "@/store/rootStore"
import { useEffect, useState } from "react"
import { z } from "zod"
import { NationalitiesFR } from "@/utils/Nationalities"
import { countriesFr } from "@/utils/conutries"
import { ProfessionList } from "@/utils/Profetion"
import React from "react"

export interface UserAdvanceInfoProps {
  onCompleted: () => void
  style?: StyleProp<ViewStyle>
  nav: any
}

// const getAdvacedData = [
//   { name: "nationality", label: "Nationalité" },
//   { name: "date_de_naissance", label: "Date de naissance" },
//   { name: "addresse", label: "Adresse" },
//   { name: "ville_de_naissance", label: "Ville de naissance" },
//   { name: "pays_des_residence", label: "Pays de résidence" },
//   { name: "profession", label: "Profession" },
//   { name: "etat_civil", label: "État civil" },
//   { name: "ville_de_residence", label: "Ville de résidence" },
// ]

const steps = [
  ["nationality", "date_de_naissance", "addresse", "ville_de_naissance"],
  ["pays_des_residence", "profession", "etat_civil", "ville_de_residence"],
]

const selectOptions: Record<string, { label: string; value: string }[]> = {
  nationality: NationalitiesFR,
  pays_des_residence: countriesFr,
  profession: ProfessionList,
  etat_civil: [
    { label: "Célibataire", value: "Célibataire" },
    { label: "Marié", value: "Marié" },
    { label: "Autre", value: "Autre" },
  ],
}

const labels: Record<string, string> = {
  nationality: "Nationalité",
  date_de_naissance: "Date de naissance",
  addresse: "Adresse",
  ville_de_naissance: "Ville de naissance",
  pays_des_residence: "Pays de résidence",
  profession: "Profession",
  etat_civil: "État civil",
  ville_de_residence: "Ville de résidence",
}

export const UserAdvanceInfo = (props: UserAdvanceInfoProps) => {
  const {
    auth: { user, fetchUserData },
  } = useStores()
  const { style, onCompleted, nav } = props
  const [isLoading, setIsLoading] = useState(false)
  const $styles = [$container, style]
  const [step, setStep] = useState(0)
  const { themed } = useAppTheme()
  const usrData = user?.PersonalInfo
  const [openDatePicker, setOpenDatePicker] = useState(false)
  const { input } = UserVerificationStyles

  type UserAdvancedSchemaType = z.infer<typeof UserAdvancedSchema>

  const {
    control,
    handleSubmit,
    setValue,
    watch, // Watching form values
    formState: { errors, isValid },
  } = useForm<UserAdvancedSchemaType>({
    resolver: zodResolver(UserAdvancedSchema),
    defaultValues: Object.fromEntries(Object.keys(labels).map((key) => [key, ""])),
    mode: "onChange", // Validate on every change
  })

  useEffect(() => {
    if (!user) {
      fetchUserData()
    }
  }, [fetchUserData, user])

  useEffect(() => {
    if (usrData) {
      ;(Object.keys(labels) as Array<keyof UserAdvancedSchemaType>).forEach((key) => {
        if (usrData && key in usrData) {
          setValue(key, usrData[key as keyof typeof usrData] || "")
        }
      })
    }
  }, [setValue, usrData])

  const onSubmit = async (formData: UserAdvancedSchemaType) => {
    setIsLoading(true)
    try {
      const formDataToSend = new FormData()
      Object.entries(formData).forEach(([key, value]) => {
        if (value) {
          formDataToSend.append(key, value)
        }
      })

      const result = await userAdvancedInfoUpdate(formDataToSend)

      if (result?.success) {
        await Promise.all([fetchUserData()])
        nav.navigate("FedhaLoader")
        Alert.alert("Succès", "Profil avancé mis à jour avec succès", [
          { text: "OK", onPress: onCompleted },
        ])
      } else {
        // Handle specific error message from API
        Alert.alert("Erreur", result?.message || "Échec de la mise à jour du profil avancé", [
          { text: "OK" },
        ])

        // Log error for debugging
        console.error("Update failed:", result?.message)

        // If it's a session error, you might want to handle it specially
        if (result?.message?.includes("Session expirée")) {
          // Optionally redirect to login or refresh token
          nav.navigate("Login")
        }
      }
    } catch (error) {
      console.error("Submission error:", error)
      Alert.alert("Erreur", "Une erreur inattendue s'est produite. Veuillez réessayer.", [
        { text: "OK" },
      ])
    } finally {
      setIsLoading(false)
    }
  }

  // Check if all fields in the current step are filled
  const currentValues = watch() // Watches all fields
  const isStepValid = steps[step].every((name) => {
    const value = currentValues[name as keyof UserAdvancedSchemaType]
    return value && value.trim() !== ""
  })

  // console.log(isStepValid)
  return (
    <View style={$styles}>
      <View style={styles.stepIndicatorContainer}>
        {steps.map((_, index) => (
          <View
            key={index}
            style={[styles.stepIndicator, index === step && styles.activeStepIndicator]}
          />
        ))}
      </View>
      {/* <View style={styles.formContainer}>
        {steps[step].map((name) => (
          <Controller
            key={name}
            control={control}
            name={name as keyof typeof UserAdvancedSchema._type}
            render={({ field }) => (
              <View>
                <FencyTextInput
                  value={field.value}
                  onChange={field.onChange}
                  style={themed($textField)}
                  inputname={labels[name]}
                  keyboardType="text"
                  placeholder={labels[name]}
                />
                {errors[name as keyof typeof UserAdvancedSchema._type] && (
                  <Text style={{ color: "red", marginBottom: spacing.xs }}>
                    {errors[name as keyof typeof UserAdvancedSchema._type]?.message}
                  </Text>
                )}
              </View>
            )}
          />
        ))}
      </View> */}
      <View style={styles.formContainer}>
        {steps[step].map((name) => (
          <Controller
            key={name}
            control={control}
            name={name as keyof UserAdvancedSchemaType}
            render={({ field }) => (
              <View>
                {name === "date_de_naissance" ? (
                  <>
                    <FencyTextInput
                      value={
                        field.value
                          ? new Date(field.value).toLocaleDateString()
                          : "Sélectionner une date"
                      }
                      onFocus={() => setOpenDatePicker(true)} // Opens picker when clicked
                      style={themed(input)}
                      inputname={labels[name]}
                      placeholder={"AAAA-MM-JJ"}
                      keyboardType="number-pad"
                      editable={false} // Prevent manual typing
                    />
                    {/* <Button
                      text={
                        field.value
                          ? new Date(field.value).toLocaleDateString()
                          : "Sélectionner une date"
                      }
                      onPress={() => setOpenDatePicker(true)}
                    /> */}
                    <DatePicker
                      modal
                      open={openDatePicker}
                      date={field.value ? new Date(field.value) : new Date()}
                      mode="date"
                      maximumDate={new Date(new Date().setFullYear(new Date().getFullYear() - 18))} // Restrict max age
                      onConfirm={(date) => {
                        const today = new Date()
                        const minAllowedDate = new Date(today.setFullYear(today.getFullYear() - 18))

                        if (date > minAllowedDate) {
                          Alert.alert("Âge invalide", "Vous devez avoir au moins 18 ans.")
                        } else {
                          setOpenDatePicker(false)
                          field.onChange(date.toISOString().split("T")[0])
                        }
                      }}
                      onCancel={() => setOpenDatePicker(false)}
                    />
                  </>
                ) : selectOptions[name] ? (
                  <CustomSelector  
                    value={field.value}
                    inputname={labels[name]}
                    onValueChange={field.onChange}
                    isitems={selectOptions[name]}
                    placeholder={{ label: labels[name], value: "" }}
                  />
                ) : (
                  <FencyTextInput
                    value={field.value}
                    onChange={field.onChange}
                    style={themed(input)}
                    inputname={labels[name]}
                    placeholder={labels[name]}
                  />
                )}
                {errors[name as keyof UserAdvancedSchemaType] && (
                  <Text style={styles.errorText}>
                    {errors[name as keyof UserAdvancedSchemaType]?.message}
                  </Text>
                )}
              </View>
            )}
          />
        ))}
      </View>

      <View style={styles.buttonContainer}>
        {step > 0 && (
          <Button
            testID="back-button"
            preset="reversed"
            text="Retour"
            style={{ flex: 1, marginRight: spacing.sm }}
            onPress={() => setStep(step - 1)}
          />
        )}
        {step < steps.length - 1 ? (
          <Button
            testID="next-button"
            preset="reversed"
            text="Suivant"
            style={{ flex: 1 }}
            onPress={() => setStep(step + 1)}
            disabled={!isStepValid} // Fix: Now correctly tracks field values
          />
        ) : (
          <Button
            testID="confirm-button"
            preset="reversed"
            text={isLoading ? "Chargement..." : "Confirmer"}
            style={{ flex: 1 }}
            disabled={isLoading || !isValid}
            onPress={handleSubmit(onSubmit)}
          />
        )}
      </View>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "center",
  alignItems: "center",
  width: "100%",
  flex: 1,
  // paddingBottom: spacing.xxl,
}

// const $textField: ThemedStyle<ViewStyle> = ({ spacing }) => ({
//   marginBottom: spacing.xs,
// })

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
  },
  stepIndicatorContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: spacing.lg,
  },
  stepIndicator: {
    width: 20,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#ccc",
    marginHorizontal: 5,
  },
  activeStepIndicator: {
    backgroundColor: colors.palette.neutral900,
  },
  formContainer: {
    width: "100%",
    marginTop: spacing.xl,
    justifyContent: "center",
    // alignItems: "center",
    marginBottom: spacing.lg,
  },
  inputContainer: {
    width: "100%",
    marginBottom: spacing.md,
  },
  errorText: {
    color: "red",
    marginBottom: spacing.xs,
  },
  buttonContainer: {
    flexDirection: "row",
    // justifyContent: "space-between",
    width: "100%",
    marginTop: spacing.md,
  },
})
