/* eslint-disable @typescript-eslint/no-unused-vars */
import { create } from "zustand"
import NetInfo from "@react-native-community/netinfo"
// import { useAuthStore } from "@/store/auth"
import { Alert } from "react-native"
import { useAuthStore } from "./AuthenticationStore"

interface NetworkState {
  isConnected: boolean
  checkInternet: () => void
}

export const useNetworkStore = create<NetworkState>((set) => ({
  isConnected: true,

  checkInternet: () => {
    NetInfo.fetch().then((state) => {
      if (!state.isConnected) {
        set({ isConnected: false })
        // Alert.alert(
        //   "Problème de Connexion",
        //   "Vous êtes hors ligne. Vérifiez votre connexion Internet.",
        //   [{ text: "OK" }],
        // )

        // ⏳ Wait 10 seconds before forcing logout
        setTimeout(() => {
          if (!useNetworkStore.getState().isConnected) {
            console.warn("❌ Internet down too long, logging out...")
            // useAuthStore.getState().logout()
            // here i should redirect the user to the Elimu page then show the user that there are not
          }
        }, 20000)
      } else {
        set({ isConnected: true })
      }
    })
  },
}))
