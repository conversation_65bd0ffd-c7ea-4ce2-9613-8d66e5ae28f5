/* eslint-disable import/no-unresolved */
import { useEffect, useRef } from "react"
import { useAuthStore } from "@/store/AuthenticationStore"
import { loadString } from "@/utils/storage"
import { navigationRef } from "@/navigators/navigationUtilities"

const TOKEN_CHECK_INTERVAL = 30000 // Check every 30 seconds

export const useTokenValidation = () => {
  const { logout, refreshAuthToken, isAuthenticated } = useAuthStore()
  const checkingRef = useRef(false)

  const validateToken = async () => {
    if (checkingRef.current) return
    checkingRef.current = true

    try {
      const accessToken = await loadString("accessToken")
      const refreshToken = await loadString("refreshToken")

      if (!accessToken || !refreshToken) {
        console.warn("❌ No tokens found, logging out...")
        handleLogout()
        return
      }

      // Try to refresh the token
      await refreshAuthToken()
    } catch (error) {
      console.error("🔴 Token validation failed:", error)
      handleLogout()
    } finally {
      checkingRef.current = false
    }
  }

  const handleLogout = () => {
    logout()
    if (navigationRef.isReady()) {
      navigationRef.reset({
        index: 0,
        routes: [{ name: "Login" }],
      })
    }
  }

  useEffect(() => {
    if (!isAuthenticated) {
      handleLogout()
      return
    }

    // Initial check
    validateToken()

    // Set up interval for periodic checks
    const interval = setInterval(validateToken, TOKEN_CHECK_INTERVAL)

    return () => {
      clearInterval(interval)
    }
  }, [isAuthenticated])

  return validateToken
}
