/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { BottomTabScreenProps, createBottomTabNavigator } from "@react-navigation/bottom-tabs"
import { CompositeScreenProps } from "@react-navigation/native"
import { TextStyle, View, ViewStyle } from "react-native"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { AppStackParamList, AppStackScreenProps } from "./AppNavigator"
import { useAppTheme } from "@/utils/useAppTheme"
import { ThemedStyle } from "@/theme"
import { Icon, Text } from "@/components"
import {
  AccountScreen,
  BusinessOnboardingScreen,
  BusinessTransactionScreen,
  DashboardBusinessScreen,
  HistoricScreen,
  LipanaFedhaScreen,
  MyStoreScreen,
  PaymeScreen,
  PochiScreen,
} from "@/screens"
import { translate } from "../i18n"

export type BusinessTabParamList = {
  AppNav: any
  DashboardBusiness: undefined
  Payme: undefined
  Pochi: undefined
  MyStore: undefined
  BusinessTransaction: undefined
  BusinessOnboarding: undefined
  Historic: undefined
  Account: undefined
  LipanaFedha: {
    fromTab?: boolean
    hideTabBar?: boolean
    scannedData?: any
  }
  // IGNITE_GENERATOR_ANCHOR_APP_STACK_PARAM_LIST
}

export type BusinessTabScreenProps<T extends keyof BusinessTabParamList> = CompositeScreenProps<
  BottomTabScreenProps<BusinessTabParamList, T>,
  AppStackScreenProps<keyof AppStackParamList>
>

const Tab = createBottomTabNavigator<BusinessTabParamList>()

/**
 * More info: https://reactnavigation.org/docs/bottom-tab-navigator/
 * @returns {JSX.Element} The rendered `TabNavigator`.
 */

export function BussinessTabNavigator({ navigation }: any) {
  const { bottom } = useSafeAreaInsets()
  const {
    themed,
    theme: { colors, spacing, typography },
  } = useAppTheme()

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarHideOnKeyboard: true,
        tabBarStyle: themed($tabBar),
        tabBarActiveTintColor: colors.palette.neutral900, // Use your primary color for active
        tabBarInactiveTintColor: colors.palette.accent400, // A softer color for inactive
        tabBarLabelStyle: themed($tabBarLabel),
        tabBarIconStyle: themed($tabBarIconStyle), // Style for the icon container
      })}
    >
      <Tab.Screen
        name="DashboardBusiness"
        component={DashboardBusinessScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.accent400,
                ...themed($tabBarLabel),
              }}
            >
              {translate("BusintabNavigator:home")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="shoppingBag"
                color={focused ? colors.palette.neutral900 : colors.palette.accent400}
                size={24}
              />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="MyStore"
        component={MyStoreScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.accent400,
                ...themed($tabBarLabel),
              }}
            >
              {translate("BusintabNavigator:mystore")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="store"
                color={focused ? colors.palette.neutral900 : colors.palette.accent400}
                size={24}
              />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Payme"
        component={PaymeScreen}
        // name="BusinessOnboarding"
        // component={BusinessOnboardingScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.accent400,
                ...themed($tabBarLabel),
              }}
            >
              {translate("BusintabNavigator:payme")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="moneyIn"
                color={focused ? colors.palette.neutral900 : colors.palette.accent400}
                size={24}
              />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="BusinessTransaction"
        component={BusinessTransactionScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.accent400,
                ...themed($tabBarLabel),
              }}
            >
              {translate("tabNavigator:history")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="orderHistory"
                color={focused ? colors.palette.neutral900 : colors.palette.accent400}
                size={24}
              />
            </View>
          ),
        }}
      />
      <Tab.Screen
        name="Account"
        component={AccountScreen}
        options={{
          tabBarLabel: ({ focused }: { focused: boolean }) => (
            <Text
              style={{
                color: focused ? colors.palette.neutral900 : colors.palette.accent400,
                ...themed($tabBarLabel),
              }}
            >
              {translate("tabNavigator:more")}
            </Text>
          ),
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <View style={themed($iconContainer)}>
              <Icon
                icon="hmenu"
                color={focused ? colors.palette.neutral900 : colors.palette.accent400}
                size={24}
              />
            </View>
          ),
        }}
      />
    </Tab.Navigator>
  )
}

const $tabBar: ThemedStyle<ViewStyle> = ({ colors, spacing }) => ({
  backgroundColor: colors.palette.neutral100, // Light background
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200, // Subtle top border
  paddingBottom: useSafeAreaInsets().bottom, // Respect safe area
  paddingTop: spacing.xs,
  height: 60 + useSafeAreaInsets().bottom, // Adjust height as needed
  alignItems: "center", // Center items vertically
})

const $tabBarLabel: ThemedStyle<TextStyle> = ({ colors, typography, spacing }) => ({
  fontSize: 12, // Readable font size
  fontFamily: typography.primary.medium,
  marginTop: spacing.xxs, // Small space between icon and label
})

const $tabBarIconStyle: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  marginBottom: spacing.xxs, // Small space between icon and label
})

const $iconContainer: ThemedStyle<ViewStyle> = ({ spacing }) => ({
  width: 30, // Adjust icon container size
  height: 30,
  justifyContent: "center",
  alignItems: "center",
})
